<Project>
  <PropertyGroup>
    <LangVersion>13</LangVersion>

    <PackageId>$(AssemblyName)</PackageId>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/mgravell/Pipelines.Sockets.Unofficial</PackageProjectUrl>
    <RepositoryUrl>https://github.com/mgravell/Pipelines.Sockets.Unofficial</RepositoryUrl>
    <PackageReleaseNotes>https://mgravell.github.io/Pipelines.Sockets.Unofficial/docs/releasenotes</PackageReleaseNotes>

    <DebugSymbols>true</DebugSymbols>
    <DebugType>embedded</DebugType>
    <LibVer>4.5.0</LibVer>
    <MSBuildWarningsAsMessages>NETSDK1069</MSBuildWarningsAsMessages>
    <CodeAnalysisRuleset>$(MSBuildThisFileDirectory)Shared.ruleset</CodeAnalysisRuleset>
    <NoWarn>NU5105</NoWarn>
    <CheckEolTargetFramework>false</CheckEolTargetFramework>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <ContinuousIntegrationBuild>true</ContinuousIntegrationBuild>
    <Deterministic>true</Deterministic>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
  </PropertyGroup>
  <ItemGroup Condition="'$(Configuration)' == 'Release' and '$(SourceRoot)'==''">
    <SourceRoot Include="$(MSBuildThisFileDirectory)/"/>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All"/>
    <PackageReference Include="Nerdbank.GitVersioning" Version="3.7.115" PrivateAssets="All" IncludeAssets="runtime;build;native;contentfiles;analyzers" />
  </ItemGroup>
</Project>