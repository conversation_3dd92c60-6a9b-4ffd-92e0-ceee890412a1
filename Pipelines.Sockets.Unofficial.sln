﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33513.286
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pipelines.Sockets.Unofficial", "src\Pipelines.Sockets.Unofficial\Pipelines.Sockets.Unofficial.csproj", "{E76D2F69-08D3-4CBB-9313-A04A84A2F0F0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{CEC5ABFF-2DBC-4A87-8E48-33B4B96F5143}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		build.cmd = build.cmd
		build.ps1 = build.ps1
		Directory.build.props = Directory.build.props
		global.json = global.json
		LICENSE = LICENSE
		NuGet.config = NuGet.config
		README.md = README.md
		Shared.ruleset = Shared.ruleset
		version.json = version.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pipelines.Sockets.Unofficial.Tests", "tests\Pipelines.Sockets.Unofficial.Tests\Pipelines.Sockets.Unofficial.Tests.csproj", "{055E629E-5734-4572-95C9-7858D46D1C86}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Toys", "Toys\Toys.csproj", "{********-906D-4159-B93A-84338C7E1463}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Benchmark", "tests\Benchmark\Benchmark.csproj", "{4D36C61E-8CBC-427F-AFEE-D810DCF81A90}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{3AC0FFD3-9F9A-4A22-89CF-5577FFEBF17C}"
	ProjectSection(SolutionItems) = preProject
		docs\arenas.md = docs\arenas.md
		docs\performance.png = docs\performance.png
		docs\releasenotes.md = docs\releasenotes.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AotTests", "tests\AotTests\AotTests.csproj", "{E134EAB4-6935-459A-9B41-E72BAE36AD14}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E76D2F69-08D3-4CBB-9313-A04A84A2F0F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E76D2F69-08D3-4CBB-9313-A04A84A2F0F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E76D2F69-08D3-4CBB-9313-A04A84A2F0F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E76D2F69-08D3-4CBB-9313-A04A84A2F0F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{055E629E-5734-4572-95C9-7858D46D1C86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{055E629E-5734-4572-95C9-7858D46D1C86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{055E629E-5734-4572-95C9-7858D46D1C86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{055E629E-5734-4572-95C9-7858D46D1C86}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-906D-4159-B93A-84338C7E1463}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-906D-4159-B93A-84338C7E1463}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-906D-4159-B93A-84338C7E1463}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-906D-4159-B93A-84338C7E1463}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D36C61E-8CBC-427F-AFEE-D810DCF81A90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D36C61E-8CBC-427F-AFEE-D810DCF81A90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D36C61E-8CBC-427F-AFEE-D810DCF81A90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D36C61E-8CBC-427F-AFEE-D810DCF81A90}.Release|Any CPU.Build.0 = Release|Any CPU
		{E134EAB4-6935-459A-9B41-E72BAE36AD14}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E134EAB4-6935-459A-9B41-E72BAE36AD14}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E134EAB4-6935-459A-9B41-E72BAE36AD14}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E134EAB4-6935-459A-9B41-E72BAE36AD14}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A573AA96-864A-4BF6-993B-12FFE38511B9}
	EndGlobalSection
EndGlobal
