image: Visual Studio 2017

init:
  - git config --global core.autocrlf input

skip_branch_with_pr: true
skip_tags: true
skip_commits:
  files:
    - '**/*.md'

environment:
  Appveyor: true

nuget:
  disable_publish_on_pr: true

build_script:
  - ps: .\build.ps1 -PullRequestNumber "$env:APPVEYOR_PULL_REQUEST_NUMBER" -CreatePackages $true

test: off
artifacts:
- path: .\.nupkgs\*.nupkg

deploy:
- provider: NuGet
  server: https://www.myget.org/F/stackoverflow/api/v2
  on:
    branch: master
  api_key:
    secure: P/UHxq2DEs0GI1SoDXDesHjRVsSVgdywz5vmsnhFQQY5aJgO3kP+QfhwfhXz19Rw
  symbol_server: https://www.myget.org/F/stackoverflow/symbols/api/v2/package
