﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net462;net472;net8.0</TargetFrameworks>
    <LangVersion>latest</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" Version="0.14.0" />
    <PackageReference Include="Nito.AsyncEx.Coordination" Version="5.1.2" />
    <ProjectReference Include="..\..\src\Pipelines.Sockets.Unofficial\Pipelines.Sockets.Unofficial.csproj" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.2" />
    <PackageReference Include="System.Collections.Immutable" Version="9.0.4" />
    <PackageReference Include="System.Reflection.Metadata" Version="9.0.4" />
  </ItemGroup>
</Project>