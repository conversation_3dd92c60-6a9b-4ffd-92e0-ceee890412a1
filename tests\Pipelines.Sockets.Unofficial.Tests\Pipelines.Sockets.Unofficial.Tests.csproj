﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net472;net8.0;net9.0</TargetFrameworks>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <ApplicationIcon />
    <SignAssembly>true</SignAssembly>
    <DelaySign>false</DelaySign>
    <AssemblyOriginatorKeyFile>Pipelines.Sockets.Unofficial.snk</AssemblyOriginatorKeyFile>
    <DefineConstants>$(DefineConstants);NO_NITO</DefineConstants>
    <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.2" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="BenchmarkDotNet" Version="0.14.0" />
  </ItemGroup>
    <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <!--<DefineConstants>$(DefineConstants);VERBOSE</DefineConstants>-->
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\somesite.pfx" Link="somesite.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\Benchmark\*Benchmarks.cs" />
    <Compile Include="..\Benchmark\Utils.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\Pipelines.Sockets.Unofficial\Pipelines.Sockets.Unofficial.csproj" />
    <ProjectReference Include="..\..\Toys\Toys.csproj" />
    <PackageReference Include="System.Collections.Immutable" Version="9.0.4" />
  </ItemGroup>
</Project>
