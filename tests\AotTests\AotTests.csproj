﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <PublishAot>true</PublishAot>

    <!-- test via: dotnet publish -c Release -->
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Pipelines.Sockets.Unofficial\Pipelines.Sockets.Unofficial.csproj" />
    <TrimmerRootAssembly Include="Pipelines.Sockets.Unofficial" />
  </ItemGroup>
</Project>
